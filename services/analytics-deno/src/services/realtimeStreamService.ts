// Real-time Streaming Service - Week 17-18 Implementation
// Core service for managing SSE connections and real-time data updates
// Optimized for <100ms latency with TimescaleDB and multi-tenant isolation

import { logger } from "../utils/logger.ts";
import { query } from "../utils/database.ts";
import { redis } from "../utils/redis.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface StreamOptions {
  dataTypes: string[];
  updateInterval: number;
  includeHistorical: boolean;
}

export interface RealtimeMetricsData {
  timestamp: string;
  tenantId: string;
  dataType: 'metrics' | 'predictions' | 'cohorts' | 'funnels' | 'clv';
  payload: {
    totalRevenue: number;
    totalOrders: number;
    conversionRate: number;
    avgOrderValue: number;
    activeUsers: number;
    churnRisk?: {
      avgProbability: number;
      highRiskCount: number;
      criticalRiskCount: number;
    };
    revenueForecasting?: {
      nextPeriodPrediction: number;
      confidenceLower: number;
      confidenceUpper: number;
    };
  };
  trends: {
    revenueChange: number;
    ordersChange: number;
    conversionChange: number;
  };
}

export interface StreamConnection {
  tenantId: string;
  controller: ReadableStreamDefaultController<Uint8Array>;
  options: StreamOptions;
  lastUpdate: Date;
  connectionId: string;
  isActive: boolean;
}

export interface HealthStatus {
  activeConnections: number;
  totalDataSent: number;
  avgLatency: number;
  uptime: number;
  lastUpdate: string;
}

export interface HistoricalDataPoint {
  timestamp: string;
  revenue: number;
  orders: number;
  customers: number;
}

export interface ConnectionStats {
  totalConnections: number;
  connectionsByTenant: Record<string, number>;
  avgLatency: number;
  totalDataSent: number;
}

export interface BroadcastResult {
  targetConnections: number;
  successCount: number;
  errorCount: number;
  message: string;
}

export interface MetricsData {
  totalRevenue: number;
  totalOrders: number;
  activeUsers: number;
  conversionRate: number;
  avgOrderValue: number;
}

export interface PredictionsData {
  churnRisk: {
    avgProbability: number;
    highRiskCount: number;
    criticalRiskCount: number;
  };
  revenueForecasting: {
    nextPeriodPrediction: number;
    confidenceLower: number;
    confidenceUpper: number;
  };
}

export interface TrendsData {
  revenueChange: number;
  ordersChange: number;
  conversionChange: number;
}

// =====================================================
// REALTIME STREAM SERVICE
// =====================================================

export class RealtimeStreamService {
  private connections: Map<string, StreamConnection> = new Map();
  private intervals: Map<string, number> = new Map();
  private startTime: Date = new Date();
  private totalDataSent: number = 0;
  private latencyHistory: number[] = [];

  constructor() {
    // Clean up inactive connections every 30 seconds
    setInterval(() => this.cleanupInactiveConnections(), 30000);
    
    // Update latency metrics every 10 seconds
    setInterval(() => this.updateLatencyMetrics(), 10000);
  }

  /**
   * Start streaming data for a tenant
   */
  public async startStream(
    tenantId: string,
    controller: ReadableStreamDefaultController<Uint8Array>,
    options: StreamOptions
  ): Promise<void> {
    const connectionId = `${tenantId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const connection: StreamConnection = {
      tenantId,
      controller,
      options,
      lastUpdate: new Date(),
      connectionId,
      isActive: true,
    };

    this.connections.set(connectionId, connection);

    logger.info("Real-time stream started", {
      tenantId,
      connectionId,
      dataTypes: options.dataTypes,
      updateInterval: options.updateInterval,
    });

    // Send initial historical data if requested
    if (options.includeHistorical) {
      await this.sendHistoricalData(connection);
    }

    // Start periodic updates
    const intervalId = setInterval(async () => {
      if (connection.isActive) {
        await this.sendRealtimeUpdate(connection);
      } else {
        clearInterval(intervalId);
        this.intervals.delete(connectionId);
      }
    }, options.updateInterval);

    this.intervals.set(connectionId, intervalId);

    // Store connection info in Redis for monitoring
    await this.updateConnectionRegistry(connectionId, connection);
  }

  /**
   * Stop streaming for a tenant
   */
  public stopStream(tenantId: string): void {
    const connectionsToStop = Array.from(this.connections.entries())
      .filter(([_, conn]) => conn.tenantId === tenantId);

    connectionsToStop.forEach(([connectionId, connection]) => {
      connection.isActive = false;
      this.connections.delete(connectionId);
      
      const intervalId = this.intervals.get(connectionId);
      if (intervalId) {
        clearInterval(intervalId);
        this.intervals.delete(connectionId);
      }

      logger.info("Real-time stream stopped", {
        tenantId,
        connectionId,
        duration: Date.now() - connection.lastUpdate.getTime(),
      });
    });

    // Clean up Redis registry
    this.cleanupConnectionRegistry(tenantId);
  }

  /**
   * Send real-time data update to a connection
   */
  private async sendRealtimeUpdate(connection: StreamConnection): Promise<void> {
    const startTime = performance.now();
    
    try {
      const data = await this.fetchRealtimeData(connection.tenantId, connection.options.dataTypes);
      
      const event = `data: ${JSON.stringify({
        type: 'update',
        timestamp: new Date().toISOString(),
        data,
        latency: performance.now() - startTime,
      })}\n\n`;

      connection.controller.enqueue(new TextEncoder().encode(event));
      connection.lastUpdate = new Date();
      this.totalDataSent++;

      // Track latency
      const latency = performance.now() - startTime;
      this.latencyHistory.push(latency);
      if (this.latencyHistory.length > 100) {
        this.latencyHistory.shift(); // Keep only last 100 measurements
      }

    } catch (error) {
      logger.error("Error sending real-time update", {
        tenantId: connection.tenantId,
        connectionId: connection.connectionId,
        error: (error as Error).message,
      });

      // Send error event to client
      const errorEvent = `data: ${JSON.stringify({
        type: 'error',
        timestamp: new Date().toISOString(),
        error: 'Failed to fetch real-time data',
        retry: true,
      })}\n\n`;

      try {
        connection.controller.enqueue(new TextEncoder().encode(errorEvent));
      } catch (controllerError) {
        // Connection likely closed, mark as inactive
        connection.isActive = false;
      }
    }
  }

  /**
   * Fetch real-time analytics data from TimescaleDB
   */
  private async fetchRealtimeData(tenantId: string, dataTypes: string[]): Promise<RealtimeMetricsData> {
    const queries = await Promise.all([
      this.fetchMetricsData(tenantId),
      dataTypes.includes('predictions') ? this.fetchPredictionsData(tenantId) : null,
      this.fetchTrendsData(tenantId),
    ]);

    const [metrics, predictions, trends] = queries;

    return {
      timestamp: new Date().toISOString(),
      tenantId,
      dataType: 'metrics',
      payload: {
        ...metrics,
        ...(predictions && { 
          churnRisk: predictions.churnRisk,
          revenueForecasting: predictions.revenueForecasting 
        }),
      },
      trends: trends || {
        revenueChange: 0,
        ordersChange: 0,
        conversionChange: 0,
      },
    };
  }

  /**
   * Fetch current metrics data
   */
  private async fetchMetricsData(tenantId: string): Promise<MetricsData> {
    const metricsQuery = `
      SELECT 
        COALESCE(SUM(revenue), 0) as total_revenue,
        COUNT(DISTINCT order_id) as total_orders,
        COUNT(DISTINCT customer_id) as active_users,
        CASE 
          WHEN COUNT(DISTINCT session_id) > 0 
          THEN COUNT(DISTINCT order_id)::float / COUNT(DISTINCT session_id)::float 
          ELSE 0 
        END as conversion_rate,
        CASE 
          WHEN COUNT(DISTINCT order_id) > 0 
          THEN SUM(revenue) / COUNT(DISTINCT order_id) 
          ELSE 0 
        END as avg_order_value
      FROM customer_events 
      WHERE tenant_id = $1 
        AND event_timestamp >= NOW() - INTERVAL '24 hours'
        AND event_type IN ('purchase', 'page_view', 'session_start')
    `;

    const result = await query(metricsQuery, [tenantId], tenantId);
    const row = result[0] || {};

    return {
      totalRevenue: parseFloat(row.total_revenue) || 0,
      totalOrders: parseInt(row.total_orders) || 0,
      activeUsers: parseInt(row.active_users) || 0,
      conversionRate: parseFloat(row.conversion_rate) || 0,
      avgOrderValue: parseFloat(row.avg_order_value) || 0,
    };
  }

  /**
   * Fetch predictions data (churn, revenue forecasting)
   */
  private fetchPredictionsData(_tenantId: string): PredictionsData {
    // This would integrate with the predictive analytics service
    // For now, return mock data that matches the expected structure
    return {
      churnRisk: {
        avgProbability: 0.32,
        highRiskCount: 45,
        criticalRiskCount: 12,
      },
      revenueForecasting: {
        nextPeriodPrediction: 125000,
        confidenceLower: 115000,
        confidenceUpper: 135000,
      },
    };
  }

  /**
   * Fetch trends data (percentage changes)
   */
  private async fetchTrendsData(tenantId: string): Promise<TrendsData> {
    const trendsQuery = `
      WITH current_period AS (
        SELECT 
          COALESCE(SUM(revenue), 0) as current_revenue,
          COUNT(DISTINCT order_id) as current_orders
        FROM customer_events 
        WHERE tenant_id = $1 
          AND event_timestamp >= NOW() - INTERVAL '24 hours'
          AND event_type = 'purchase'
      ),
      previous_period AS (
        SELECT 
          COALESCE(SUM(revenue), 0) as previous_revenue,
          COUNT(DISTINCT order_id) as previous_orders
        FROM customer_events 
        WHERE tenant_id = $1 
          AND event_timestamp >= NOW() - INTERVAL '48 hours'
          AND event_timestamp < NOW() - INTERVAL '24 hours'
          AND event_type = 'purchase'
      )
      SELECT 
        CASE 
          WHEN p.previous_revenue > 0 
          THEN ((c.current_revenue - p.previous_revenue) / p.previous_revenue) * 100
          ELSE 0 
        END as revenue_change,
        CASE 
          WHEN p.previous_orders > 0 
          THEN ((c.current_orders - p.previous_orders)::float / p.previous_orders::float) * 100
          ELSE 0 
        END as orders_change
      FROM current_period c, previous_period p
    `;

    const result = await query(trendsQuery, [tenantId], tenantId);
    const row = result[0] || {};

    return {
      revenueChange: parseFloat(row.revenue_change) || 0,
      ordersChange: parseFloat(row.orders_change) || 0,
      conversionChange: 0, // Would be calculated similarly
    };
  }

  /**
   * Send historical data for initial connection
   */
  private async sendHistoricalData(connection: StreamConnection): Promise<void> {
    try {
      const historicalData = await this.fetchHistoricalData(
        connection.tenantId,
        connection.options.dataTypes
      );

      const event = `data: ${JSON.stringify({
        type: 'historical',
        timestamp: new Date().toISOString(),
        data: historicalData,
      })}\n\n`;

      connection.controller.enqueue(new TextEncoder().encode(event));

      logger.debug("Historical data sent", {
        tenantId: connection.tenantId,
        connectionId: connection.connectionId,
        dataPoints: historicalData.length,
      });

    } catch (error) {
      logger.error("Error sending historical data", {
        tenantId: connection.tenantId,
        connectionId: connection.connectionId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Fetch historical data for initial load
   */
  private async fetchHistoricalData(tenantId: string, _dataTypes: string[]): Promise<HistoricalDataPoint[]> {
    const historicalQuery = `
      SELECT
        DATE_TRUNC('hour', event_timestamp) as hour,
        COALESCE(SUM(revenue), 0) as revenue,
        COUNT(DISTINCT order_id) as orders,
        COUNT(DISTINCT customer_id) as customers
      FROM customer_events
      WHERE tenant_id = $1
        AND event_timestamp >= NOW() - INTERVAL '24 hours'
        AND event_type = 'purchase'
      GROUP BY DATE_TRUNC('hour', event_timestamp)
      ORDER BY hour DESC
      LIMIT 24
    `;

    const result = await query(historicalQuery, [tenantId], tenantId);

    return (result as Record<string, unknown>[]).map((row) => ({
      timestamp: String(row.hour || ''),
      revenue: parseFloat(String(row.revenue || '0')),
      orders: parseInt(String(row.orders || '0')),
      customers: parseInt(String(row.customers || '0')),
    }));
  }

  /**
   * Clean up inactive connections
   */
  private cleanupInactiveConnections(): void {
    const now = new Date();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes

    const inactiveConnections = Array.from(this.connections.entries())
      .filter(([_, conn]) =>
        !conn.isActive || (now.getTime() - conn.lastUpdate.getTime()) > inactiveThreshold
      );

    inactiveConnections.forEach(([connectionId, connection]) => {
      connection.isActive = false;
      this.connections.delete(connectionId);

      const intervalId = this.intervals.get(connectionId);
      if (intervalId) {
        clearInterval(intervalId);
        this.intervals.delete(connectionId);
      }

      logger.debug("Cleaned up inactive connection", {
        tenantId: connection.tenantId,
        connectionId,
        lastUpdate: connection.lastUpdate,
      });
    });
  }

  /**
   * Update latency metrics
   */
  private updateLatencyMetrics(): void {
    if (this.latencyHistory.length === 0) return;

    const avgLatency = this.latencyHistory.reduce((sum, lat) => sum + lat, 0) / this.latencyHistory.length;

    // Store metrics in Redis for monitoring
    redis.setex(`realtime:metrics:latency`, 300, avgLatency.toString()).catch(error => {
      logger.error("Failed to store latency metrics", { error: error.message });
    });
  }

  /**
   * Update connection registry in Redis
   */
  private async updateConnectionRegistry(connectionId: string, connection: StreamConnection): Promise<void> {
    try {
      const connectionInfo = {
        tenantId: connection.tenantId,
        connectionId,
        startTime: connection.lastUpdate.toISOString(),
        dataTypes: connection.options.dataTypes,
        updateInterval: connection.options.updateInterval,
      };

      await redis.setex(
        `realtime:connections:${connectionId}`,
        3600, // 1 hour TTL
        JSON.stringify(connectionInfo)
      );

      // Update tenant connection count
      await redis.incr(`realtime:tenant:${connection.tenantId}:connections`);
      await redis.expire(`realtime:tenant:${connection.tenantId}:connections`, 3600);

    } catch (error) {
      logger.error("Failed to update connection registry", {
        connectionId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Clean up connection registry
   */
  private async cleanupConnectionRegistry(tenantId: string): Promise<void> {
    try {
      // Decrement tenant connection count
      const count = await redis.decr(`realtime:tenant:${tenantId}:connections`);
      if (count <= 0) {
        await redis.del(`realtime:tenant:${tenantId}:connections`);
      }
    } catch (error) {
      logger.error("Failed to cleanup connection registry", {
        tenantId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Get health status of streaming service
   */
  public getHealthStatus(): HealthStatus {
    const activeConnections = this.connections.size;
    const avgLatency = this.latencyHistory.length > 0
      ? this.latencyHistory.reduce((sum, lat) => sum + lat, 0) / this.latencyHistory.length
      : 0;
    const uptime = Date.now() - this.startTime.getTime();

    return {
      activeConnections,
      totalDataSent: this.totalDataSent,
      avgLatency: Math.round(avgLatency * 100) / 100,
      uptime: Math.round(uptime / 1000), // seconds
      lastUpdate: new Date().toISOString(),
    };
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): ConnectionStats {
    const connectionsByTenant = new Map<string, number>();

    this.connections.forEach(conn => {
      const count = connectionsByTenant.get(conn.tenantId) || 0;
      connectionsByTenant.set(conn.tenantId, count + 1);
    });

    return {
      totalConnections: this.connections.size,
      connectionsByTenant: Object.fromEntries(connectionsByTenant),
      avgLatency: this.latencyHistory.length > 0
        ? this.latencyHistory.reduce((sum, lat) => sum + lat, 0) / this.latencyHistory.length
        : 0,
      totalDataSent: this.totalDataSent,
    };
  }

  /**
   * Broadcast message to all or specific tenant connections
   */
  public broadcastMessage(message: unknown, tenantId?: string): BroadcastResult {
    const targetConnections = tenantId
      ? Array.from(this.connections.values()).filter(conn => conn.tenantId === tenantId)
      : Array.from(this.connections.values());

    const event = `data: ${JSON.stringify({
      type: 'broadcast',
      timestamp: new Date().toISOString(),
      message,
    })}\n\n`;

    const encodedEvent = new TextEncoder().encode(event);
    let successCount = 0;
    let errorCount = 0;

    for (const connection of targetConnections) {
      try {
        connection.controller.enqueue(encodedEvent);
        successCount++;
      } catch (error) {
        errorCount++;
        connection.isActive = false;
      }
    }

    return {
      targetConnections: targetConnections.length,
      successCount,
      errorCount,
      message: typeof message === 'string' ? message : 'Broadcast sent',
    };
  }
}
