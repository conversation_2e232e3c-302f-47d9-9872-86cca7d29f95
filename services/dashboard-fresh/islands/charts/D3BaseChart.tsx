// D3.js Base Chart Component for Fresh Islands
// Week 17-18 Implementation: Foundation component for all D3.js visualizations

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal, computed } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions, 
  DEFAULT_CHART_CONFIG,
  createResponsiveDimensions,
  debounce,
  createTooltip,
  showTooltip,
  hideTooltip
} from "../../utils/d3-base.ts";

// =====================================================
// BASE CHART PROPS INTERFACE
// =====================================================

export interface D3BaseChartProps {
  data: any[];
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  responsive?: boolean;
  loading?: boolean;
  error?: string | null;
  onDataClick?: (data: any, event: MouseEvent) => void;
  onChartReady?: (chartInstance: D3BaseChart) => void;
}

// =====================================================
// BASE CHART CLASS
// =====================================================

export abstract class D3BaseChart {
  protected svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  protected container: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  protected tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  protected dimensions: ChartDimensions;
  protected data: any[];
  protected isInitialized = false;

  constructor(
    svgElement: SVGSVGElement,
    containerElement: HTMLDivElement,
    dimensions: ChartDimensions,
    data: any[]
  ) {
    this.svg = d3.select(svgElement);
    this.container = d3.select(containerElement);
    this.dimensions = dimensions;
    this.data = data;
    this.tooltip = createTooltip(containerElement);
    this.initialize();
  }

  // Abstract methods to be implemented by subclasses
  abstract render(): void;
  abstract update(newData: any[]): void;
  abstract resize(newDimensions: ChartDimensions): void;

  // Base initialization
  protected initialize(): void {
    this.svg
      .attr("width", this.dimensions.width)
      .attr("height", this.dimensions.height)
      .attr("viewBox", `0 0 ${this.dimensions.width} ${this.dimensions.height}`)
      .style("max-width", "100%")
      .style("height", "auto");

    this.isInitialized = true;
  }

  // Clear chart content
  protected clear(): void {
    this.svg.selectAll("*").remove();
  }

  // Update data and re-render
  public updateData(newData: any[]): void {
    this.data = newData;
    this.update(newData);
  }

  // Update dimensions and re-render
  public updateDimensions(newDimensions: ChartDimensions): void {
    this.dimensions = newDimensions;
    this.svg
      .attr("width", newDimensions.width)
      .attr("height", newDimensions.height)
      .attr("viewBox", `0 0 ${newDimensions.width} ${newDimensions.height}`);
    this.resize(newDimensions);
  }

  // Show tooltip helper
  protected showTooltip(content: string, event: MouseEvent): void {
    showTooltip(this.tooltip, content, event);
  }

  // Hide tooltip helper
  protected hideTooltip(): void {
    hideTooltip(this.tooltip);
  }

  // Cleanup
  public destroy(): void {
    this.tooltip.remove();
    this.clear();
  }
}

// =====================================================
// BASE CHART COMPONENT
// =====================================================

export default function D3BaseChartComponent<T extends D3BaseChart>({
  data,
  width,
  height,
  title,
  className = "",
  responsive = true,
  loading = false,
  error = null,
  onDataClick,
  onChartReady,
  chartClass,
  chartOptions = {}
}: D3BaseChartProps & {
  chartClass: new (
    svg: SVGSVGElement,
    container: HTMLDivElement,
    dimensions: ChartDimensions,
    data: any[],
    options?: any
  ) => T;
  chartOptions?: any;
}) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<T | null>(null);
  
  const containerWidth = useSignal(width || DEFAULT_CHART_CONFIG.dimensions.width);
  const containerHeight = useSignal(height || DEFAULT_CHART_CONFIG.dimensions.height);
  
  const dimensions = computed(() => {
    if (responsive && containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      return createResponsiveDimensions(rect.width || containerWidth.value);
    }
    return {
      width: containerWidth.value,
      height: containerHeight.value,
      margin: DEFAULT_CHART_CONFIG.dimensions.margin
    };
  });

  // Initialize chart when component mounts
  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !containerRef.current || !data.length) {
      return;
    }

    try {
      // Create chart instance
      chartInstanceRef.current = new chartClass(
        svgRef.current,
        containerRef.current,
        dimensions.value,
        data,
        chartOptions
      );

      // Render initial chart
      chartInstanceRef.current.render();

      // Notify parent component
      onChartReady?.(chartInstanceRef.current);

    } catch (err) {
      console.error("Error initializing D3 chart:", err);
    }

    // Cleanup on unmount
    return () => {
      chartInstanceRef.current?.destroy();
    };
  }, [data, chartClass]);

  // Update chart when data changes
  useEffect(() => {
    if (chartInstanceRef.current && data.length > 0) {
      chartInstanceRef.current.updateData(data);
    }
  }, [data]);

  // Update chart when dimensions change
  useEffect(() => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.updateDimensions(dimensions.value);
    }
  }, [dimensions.value]);

  // Handle window resize for responsive charts
  useEffect(() => {
    if (!IS_BROWSER || !responsive) return;

    const handleResize = debounce(() => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        containerWidth.value = rect.width;
      }
    }, 250);

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [responsive]);

  // Handle data click events
  useEffect(() => {
    if (!chartInstanceRef.current || !onDataClick) return;

    const handleClick = (event: MouseEvent) => {
      // This would be implemented by specific chart types
      // to extract the clicked data point
    };

    const svgElement = svgRef.current;
    svgElement?.addEventListener("click", handleClick);
    
    return () => {
      svgElement?.removeEventListener("click", handleClick);
    };
  }, [onDataClick]);

  // Render loading state
  if (loading) {
    return (
      <div className={`d3-chart-container ${className}`} ref={containerRef}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
            {title}
          </h3>
        )}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading chart...</span>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`d3-chart-container ${className}`} ref={containerRef}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
            {title}
          </h3>
        )}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <div className="text-red-600 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-red-700 font-medium">Chart Error</p>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Render empty state
  if (!data.length) {
    return (
      <div className={`d3-chart-container ${className}`} ref={containerRef}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
            {title}
          </h3>
        )}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <div className="text-gray-400 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-gray-600 font-medium">No Data Available</p>
            <p className="text-gray-500 text-sm mt-1">Chart will appear when data is loaded</p>
          </div>
        </div>
      </div>
    );
  }

  // Render chart
  return (
    <div className={`d3-chart-container relative ${className}`} ref={containerRef}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
          {title}
        </h3>
      )}
      <svg
        ref={svgRef}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
    </div>
  );
}
