// D3.js CLV Histogram Visualization Component
// Week 17-18 Implementation: Customer Lifetime Value distribution with statistical overlays

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions,
  DEFAULT_CHART_CONFIG,
  getInnerDimensions,
  formatNumber,
  createLinearScale,
  animateEntrance,
  addHoverEffects
} from "../../utils/d3-base.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface CLVHistogramData {
  clvRange: string;
  customerCount: number;
  totalClv: number;
  percentage: number;
  rangeMin: number;
  rangeMax: number;
}

export interface CLVStatistics {
  mean: number;
  median: number;
  mode: number;
  standardDeviation: number;
  percentiles: {
    p25: number;
    p50: number;
    p75: number;
    p90: number;
    p95: number;
  };
}

export interface D3CLVHistogramProps {
  data: CLVHistogramData[];
  statistics?: CLVStatistics;
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  showStatistics?: boolean;
  enableBrushing?: boolean;
  onBrushSelection?: (range: [number, number] | null) => void;
  onBarClick?: (data: CLVHistogramData, event: MouseEvent) => void;
}

// =====================================================
// CLV HISTOGRAM CHART CLASS
// =====================================================

class CLVHistogramChart {
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private container: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private dimensions: ChartDimensions;
  private data: CLVHistogramData[];
  private statistics?: CLVStatistics;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private brush?: d3.BrushBehavior<unknown>;
  private showStatistics: boolean;
  private enableBrushing: boolean;

  constructor(
    svgElement: SVGSVGElement,
    containerElement: HTMLDivElement,
    dimensions: ChartDimensions,
    data: CLVHistogramData[],
    options: { 
      statistics?: CLVStatistics;
      showStatistics?: boolean;
      enableBrushing?: boolean;
    } = {}
  ) {
    this.svg = d3.select(svgElement);
    this.container = d3.select(containerElement);
    this.dimensions = dimensions;
    this.data = data;
    this.statistics = options.statistics;
    this.showStatistics = options.showStatistics || false;
    this.enableBrushing = options.enableBrushing || false;
    
    // Create tooltip
    this.tooltip = this.container
      .append("div")
      .attr("class", "clv-histogram-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background-color", "rgba(0, 0, 0, 0.9)")
      .style("color", "white")
      .style("padding", "12px")
      .style("border-radius", "6px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    this.initialize();
  }

  private initialize(): void {
    const { width, height } = this.dimensions;
    
    this.svg
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style("max-width", "100%")
      .style("height", "auto");

    this.setupScales();
    this.render();
  }

  private setupScales(): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // X-scale for CLV ranges
    const clvExtent = d3.extent(this.data.flatMap(d => [d.rangeMin, d.rangeMax])) as [number, number];
    this.xScale = d3.scaleLinear()
      .domain(clvExtent)
      .range([0, innerWidth])
      .nice();

    // Y-scale for customer counts
    const maxCount = d3.max(this.data, d => d.customerCount) || 0;
    this.yScale = d3.scaleLinear()
      .domain([0, maxCount])
      .range([innerHeight, 0])
      .nice();

    // Setup brush if enabled
    if (this.enableBrushing) {
      this.brush = d3.brushX()
        .extent([[0, 0], [innerWidth, innerHeight]])
        .on("brush end", (event) => this.handleBrush(event));
    }
  }

  public render(): void {
    this.svg.selectAll("*").remove();

    const { margin } = this.dimensions;
    const g = this.svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    this.renderAxes(g);
    this.renderGridLines(g);
    this.renderHistogramBars(g);
    
    if (this.showStatistics && this.statistics) {
      this.renderStatisticalOverlays(g);
    }
    
    if (this.enableBrushing && this.brush) {
      this.renderBrush(g);
    }
  }

  private renderAxes(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // X-axis
    const xAxis = d3.axisBottom(this.xScale)
      .tickFormat(d => formatNumber(d, 'currency'));

    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280")
      .attr("transform", "rotate(-45)")
      .style("text-anchor", "end");

    // Y-axis
    const yAxis = d3.axisLeft(this.yScale)
      .tickFormat(d => formatNumber(d, 'integer'));

    g.append("g")
      .attr("class", "y-axis")
      .call(yAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280");

    // Axis labels
    g.append("text")
      .attr("class", "x-axis-label")
      .attr("x", innerWidth / 2)
      .attr("y", innerHeight + 50)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Customer Lifetime Value");

    g.append("text")
      .attr("class", "y-axis-label")
      .attr("transform", "rotate(-90)")
      .attr("x", -innerHeight / 2)
      .attr("y", -35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Number of Customers");
  }

  private renderGridLines(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // Horizontal grid lines
    g.selectAll(".grid-line-horizontal")
      .data(this.yScale.ticks())
      .enter()
      .append("line")
      .attr("class", "grid-line-horizontal")
      .attr("x1", 0)
      .attr("x2", innerWidth)
      .attr("y1", d => this.yScale(d))
      .attr("y2", d => this.yScale(d))
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);
  }

  private renderHistogramBars(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const bars = g.selectAll(".histogram-bar")
      .data(this.data)
      .enter()
      .append("rect")
      .attr("class", "histogram-bar")
      .attr("x", d => this.xScale(d.rangeMin))
      .attr("y", d => this.yScale(d.customerCount))
      .attr("width", d => Math.max(0, this.xScale(d.rangeMax) - this.xScale(d.rangeMin) - 1))
      .attr("height", d => this.yScale(0) - this.yScale(d.customerCount))
      .style("fill", DEFAULT_CHART_CONFIG.colors.primary)
      .style("stroke", "#fff")
      .style("stroke-width", 1)
      .style("cursor", "pointer");

    // Add hover effects
    addHoverEffects(
      bars,
      (event, d) => {
        d3.select(event.currentTarget).style("fill", DEFAULT_CHART_CONFIG.colors.accent);
        this.showTooltip(d, event);
      },
      (event, d) => {
        d3.select(event.currentTarget).style("fill", DEFAULT_CHART_CONFIG.colors.primary);
        this.hideTooltip();
      }
    );

    // Add click handlers
    bars.on("click", (event, d) => {
      const customEvent = new CustomEvent("barClick", { detail: { data: d, event } });
      this.container.node()?.dispatchEvent(customEvent);
    });

    // Add percentage labels on top of bars
    g.selectAll(".bar-label")
      .data(this.data.filter(d => d.percentage > 2)) // Only show labels for significant bars
      .enter()
      .append("text")
      .attr("class", "bar-label")
      .attr("x", d => this.xScale(d.rangeMin) + (this.xScale(d.rangeMax) - this.xScale(d.rangeMin)) / 2)
      .attr("y", d => this.yScale(d.customerCount) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .style("pointer-events", "none")
      .text(d => formatNumber(d.percentage, 'percentage'));

    // Animate entrance
    animateEntrance(bars);
  }

  private renderStatisticalOverlays(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    if (!this.statistics) return;

    const { height: innerHeight } = getInnerDimensions(this.dimensions);
    const stats = this.statistics;

    // Mean line
    g.append("line")
      .attr("class", "mean-line")
      .attr("x1", this.xScale(stats.mean))
      .attr("x2", this.xScale(stats.mean))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", DEFAULT_CHART_CONFIG.colors.danger)
      .style("stroke-width", 2)
      .style("stroke-dasharray", "5,5");

    // Median line
    g.append("line")
      .attr("class", "median-line")
      .attr("x1", this.xScale(stats.median))
      .attr("x2", this.xScale(stats.median))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", DEFAULT_CHART_CONFIG.colors.success)
      .style("stroke-width", 2)
      .style("stroke-dasharray", "3,3");

    // Percentile markers
    const percentileData = [
      { value: stats.percentiles.p25, label: "25th" },
      { value: stats.percentiles.p75, label: "75th" },
      { value: stats.percentiles.p90, label: "90th" },
      { value: stats.percentiles.p95, label: "95th" }
    ];

    g.selectAll(".percentile-line")
      .data(percentileData)
      .enter()
      .append("line")
      .attr("class", "percentile-line")
      .attr("x1", d => this.xScale(d.value))
      .attr("x2", d => this.xScale(d.value))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", DEFAULT_CHART_CONFIG.colors.neutral)
      .style("stroke-width", 1)
      .style("stroke-dasharray", "2,2")
      .style("opacity", 0.7);

    // Statistics legend
    this.renderStatisticsLegend(g);
  }

  private renderStatisticsLegend(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    if (!this.statistics) return;

    const legendData = [
      { label: "Mean", color: DEFAULT_CHART_CONFIG.colors.danger, value: this.statistics.mean },
      { label: "Median", color: DEFAULT_CHART_CONFIG.colors.success, value: this.statistics.median }
    ];

    const legend = g.append("g")
      .attr("class", "statistics-legend")
      .attr("transform", "translate(10, 10)");

    const legendItems = legend.selectAll(".legend-item")
      .data(legendData)
      .enter()
      .append("g")
      .attr("class", "legend-item")
      .attr("transform", (d, i) => `translate(0, ${i * 20})`);

    // Legend lines
    legendItems.append("line")
      .attr("x1", 0)
      .attr("x2", 20)
      .attr("y1", 6)
      .attr("y2", 6)
      .style("stroke", d => d.color)
      .style("stroke-width", 2)
      .style("stroke-dasharray", (d, i) => i === 0 ? "5,5" : "3,3");

    // Legend labels
    legendItems.append("text")
      .attr("x", 25)
      .attr("y", 6)
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("fill", "#374151")
      .text(d => `${d.label}: ${formatNumber(d.value, 'currency')}`);
  }

  private renderBrush(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    if (!this.brush) return;

    g.append("g")
      .attr("class", "brush")
      .call(this.brush);
  }

  private handleBrush(event: any): void {
    const selection = event.selection;
    if (selection) {
      const [x0, x1] = selection.map(this.xScale.invert);
      const customEvent = new CustomEvent("brushSelection", { 
        detail: { range: [x0, x1] } 
      });
      this.container.node()?.dispatchEvent(customEvent);
    } else {
      const customEvent = new CustomEvent("brushSelection", { 
        detail: { range: null } 
      });
      this.container.node()?.dispatchEvent(customEvent);
    }
  }

  private showTooltip(data: CLVHistogramData, event: MouseEvent): void {
    const content = `
      <div class="font-semibold mb-1">${data.clvRange}</div>
      <div>Customers: ${formatNumber(data.customerCount, 'integer')}</div>
      <div>Total CLV: ${formatNumber(data.totalClv, 'currency')}</div>
      <div>Percentage: ${formatNumber(data.percentage, 'percentage')}</div>
      <div>Avg CLV: ${formatNumber(data.totalClv / data.customerCount, 'currency')}</div>
    `;

    this.tooltip
      .style("visibility", "visible")
      .html(content)
      .style("left", `${event.pageX + 10}px`)
      .style("top", `${event.pageY - 10}px`);
  }

  private hideTooltip(): void {
    this.tooltip.style("visibility", "hidden");
  }

  public updateData(newData: CLVHistogramData[], newStatistics?: CLVStatistics): void {
    this.data = newData;
    this.statistics = newStatistics;
    this.setupScales();
    this.render();
  }

  public updateDimensions(newDimensions: ChartDimensions): void {
    this.dimensions = newDimensions;
    this.svg
      .attr("width", newDimensions.width)
      .attr("height", newDimensions.height)
      .attr("viewBox", `0 0 ${newDimensions.width} ${newDimensions.height}`);
    this.setupScales();
    this.render();
  }

  public destroy(): void {
    this.tooltip.remove();
    this.svg.selectAll("*").remove();
  }
}

// =====================================================
// REACT COMPONENT
// =====================================================

export default function D3CLVHistogram({
  data,
  statistics,
  width = 800,
  height = 400,
  title = "Customer Lifetime Value Distribution",
  className = "",
  showStatistics = true,
  enableBrushing = false,
  onBrushSelection,
  onBarClick
}: D3CLVHistogramProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<CLVHistogramChart | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !containerRef.current || !data.length) {
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const dimensions: ChartDimensions = {
        width,
        height,
        margin: { top: 20, right: 30, bottom: 70, left: 60 }
      };

      chartRef.current = new CLVHistogramChart(
        svgRef.current,
        containerRef.current,
        dimensions,
        data,
        { statistics, showStatistics, enableBrushing }
      );

      loading.value = false;
    } catch (err) {
      console.error("Error creating CLV histogram:", err);
      error.value = err instanceof Error ? err.message : "Unknown error";
      loading.value = false;
    }

    return () => {
      chartRef.current?.destroy();
    };
  }, [data, statistics, width, height, showStatistics, enableBrushing]);

  // Handle brush selection events
  useEffect(() => {
    if (!containerRef.current || !onBrushSelection) return;

    const handleBrushSelection = (event: CustomEvent) => {
      const { range } = event.detail;
      onBrushSelection(range);
    };

    containerRef.current.addEventListener("brushSelection", handleBrushSelection as EventListener);
    
    return () => {
      containerRef.current?.removeEventListener("brushSelection", handleBrushSelection as EventListener);
    };
  }, [onBrushSelection]);

  // Handle bar click events
  useEffect(() => {
    if (!containerRef.current || !onBarClick) return;

    const handleBarClick = (event: CustomEvent) => {
      const { data: barData, event: mouseEvent } = event.detail;
      onBarClick(barData, mouseEvent);
    };

    containerRef.current.addEventListener("barClick", handleBarClick as EventListener);
    
    return () => {
      containerRef.current?.removeEventListener("barClick", handleBarClick as EventListener);
    };
  }, [onBarClick]);

  if (loading.value) {
    return (
      <div className={`clv-histogram-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading histogram...</span>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div className={`clv-histogram-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <p className="text-red-700 font-medium">Error Loading Histogram</p>
            <p className="text-red-600 text-sm mt-1">{error.value}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data.length) {
    return (
      <div className={`clv-histogram-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <p className="text-gray-600 font-medium">No CLV Data Available</p>
            <p className="text-gray-500 text-sm mt-1">Histogram will appear when data is loaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`clv-histogram-container relative ${className}`} ref={containerRef}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      <svg
        ref={svgRef}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
      {enableBrushing && (
        <p className="text-sm text-gray-500 mt-2 text-center">
          Drag to select a CLV range for detailed analysis
        </p>
      )}
    </div>
  );
}
