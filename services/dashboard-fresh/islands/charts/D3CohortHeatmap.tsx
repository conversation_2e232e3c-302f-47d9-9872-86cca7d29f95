// D3.js Cohort Heatmap Visualization Component
// Week 17-18 Implementation: Interactive cohort retention heatmap

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions,
  DEFAULT_CHART_CONFIG,
  getInnerDimensions,
  formatNumber,
  formatDate,
  animateEntrance,
  addHoverEffects
} from "../../utils/d3-base.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface CohortHeatmapData {
  cohortId: string;
  cohortDate: string;
  period: number;
  retentionRate: number;
  customerCount: number;
}

export interface D3CohortHeatmapProps {
  data: CohortHeatmapData[];
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  onCellClick?: (data: CohortHeatmapData, event: MouseEvent) => void;
  onCellHover?: (data: CohortHeatmapData | null) => void;
}

// =====================================================
// COHORT HEATMAP CHART CLASS
// =====================================================

class CohortHeatmapChart {
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private container: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private dimensions: ChartDimensions;
  private data: CohortHeatmapData[];
  private colorScale: d3.ScaleSequential<string>;
  private xScale: d3.ScaleBand<string>;
  private yScale: d3.ScaleBand<string>;

  constructor(
    svgElement: SVGSVGElement,
    containerElement: HTMLDivElement,
    dimensions: ChartDimensions,
    data: CohortHeatmapData[]
  ) {
    this.svg = d3.select(svgElement);
    this.container = d3.select(containerElement);
    this.dimensions = dimensions;
    this.data = data;
    
    // Create tooltip
    this.tooltip = this.container
      .append("div")
      .attr("class", "cohort-heatmap-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background-color", "rgba(0, 0, 0, 0.9)")
      .style("color", "white")
      .style("padding", "12px")
      .style("border-radius", "6px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    this.initialize();
  }

  private initialize(): void {
    const { width, height } = this.dimensions;
    
    this.svg
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style("max-width", "100%")
      .style("height", "auto");

    this.setupScales();
    this.render();
  }

  private setupScales(): void {
    const { width, height, margin } = this.dimensions;
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // Get unique cohorts and periods
    const cohorts = Array.from(new Set(this.data.map(d => d.cohortDate))).sort();
    const periods = Array.from(new Set(this.data.map(d => d.period))).sort((a, b) => a - b);

    // Create scales
    this.xScale = d3.scaleBand()
      .domain(periods.map(String))
      .range([0, innerWidth])
      .padding(0.05);

    this.yScale = d3.scaleBand()
      .domain(cohorts)
      .range([0, innerHeight])
      .padding(0.05);

    // Color scale for retention rates
    const maxRetention = d3.max(this.data, d => d.retentionRate) || 100;
    this.colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, maxRetention]);
  }

  public render(): void {
    this.svg.selectAll("*").remove();

    const { margin } = this.dimensions;
    const g = this.svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    this.renderHeatmapCells(g);
    this.renderAxes(g);
    this.renderLegend(g);
  }

  private renderHeatmapCells(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const cells = g.selectAll(".heatmap-cell")
      .data(this.data)
      .enter()
      .append("rect")
      .attr("class", "heatmap-cell")
      .attr("x", d => this.xScale(String(d.period)) || 0)
      .attr("y", d => this.yScale(d.cohortDate) || 0)
      .attr("width", this.xScale.bandwidth())
      .attr("height", this.yScale.bandwidth())
      .attr("fill", d => this.colorScale(d.retentionRate))
      .attr("stroke", "#fff")
      .attr("stroke-width", 1)
      .style("cursor", "pointer");

    // Add hover effects and tooltips
    addHoverEffects(
      cells,
      (event, d) => this.showTooltip(d, event),
      () => this.hideTooltip()
    );

    // Add click handlers
    cells.on("click", (event, d) => {
      // Emit click event to parent component
      const customEvent = new CustomEvent("cellClick", { detail: { data: d, event } });
      this.container.node()?.dispatchEvent(customEvent);
    });

    // Add cell labels for better readability
    g.selectAll(".cell-label")
      .data(this.data.filter(d => d.retentionRate > 10)) // Only show labels for significant values
      .enter()
      .append("text")
      .attr("class", "cell-label")
      .attr("x", d => (this.xScale(String(d.period)) || 0) + this.xScale.bandwidth() / 2)
      .attr("y", d => (this.yScale(d.cohortDate) || 0) + this.yScale.bandwidth() / 2)
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .style("font-size", "10px")
      .style("font-weight", "500")
      .style("fill", d => d.retentionRate > 50 ? "#fff" : "#333")
      .style("pointer-events", "none")
      .text(d => formatNumber(d.retentionRate, 'percentage'));

    // Animate entrance
    animateEntrance(cells);
  }

  private renderAxes(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);

    // X-axis (periods)
    const xAxis = d3.axisBottom(this.xScale)
      .tickFormat(d => `Period ${d}`);

    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280");

    // Y-axis (cohorts)
    const yAxis = d3.axisLeft(this.yScale)
      .tickFormat(d => formatDate(d, 'short'));

    g.append("g")
      .attr("class", "y-axis")
      .call(yAxis)
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", "#6b7280");

    // Axis labels
    g.append("text")
      .attr("class", "x-axis-label")
      .attr("x", innerWidth / 2)
      .attr("y", innerHeight + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Time Period");

    g.append("text")
      .attr("class", "y-axis-label")
      .attr("transform", "rotate(-90)")
      .attr("x", -innerHeight / 2)
      .attr("y", -35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Cohort");
  }

  private renderLegend(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth } = getInnerDimensions(this.dimensions);
    const legendWidth = 200;
    const legendHeight = 10;
    const legendX = innerWidth - legendWidth;
    const legendY = -15;

    // Create gradient for legend
    const defs = this.svg.append("defs");
    const gradient = defs.append("linearGradient")
      .attr("id", "cohort-heatmap-gradient")
      .attr("x1", "0%")
      .attr("x2", "100%")
      .attr("y1", "0%")
      .attr("y2", "0%");

    const steps = 10;
    for (let i = 0; i <= steps; i++) {
      const percent = (i / steps) * 100;
      const value = (i / steps) * (d3.max(this.data, d => d.retentionRate) || 100);
      gradient.append("stop")
        .attr("offset", `${percent}%`)
        .attr("stop-color", this.colorScale(value));
    }

    // Legend rectangle
    g.append("rect")
      .attr("x", legendX)
      .attr("y", legendY)
      .attr("width", legendWidth)
      .attr("height", legendHeight)
      .style("fill", "url(#cohort-heatmap-gradient)")
      .style("stroke", "#d1d5db")
      .style("stroke-width", 1);

    // Legend labels
    const maxRetention = d3.max(this.data, d => d.retentionRate) || 100;
    g.append("text")
      .attr("x", legendX)
      .attr("y", legendY - 3)
      .style("font-size", "10px")
      .style("fill", "#6b7280")
      .text("0%");

    g.append("text")
      .attr("x", legendX + legendWidth)
      .attr("y", legendY - 3)
      .attr("text-anchor", "end")
      .style("font-size", "10px")
      .style("fill", "#6b7280")
      .text(formatNumber(maxRetention, 'percentage'));

    g.append("text")
      .attr("x", legendX + legendWidth / 2)
      .attr("y", legendY - 15)
      .attr("text-anchor", "middle")
      .style("font-size", "11px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text("Retention Rate");
  }

  private showTooltip(data: CohortHeatmapData, event: MouseEvent): void {
    const content = `
      <div class="font-semibold mb-1">Cohort: ${formatDate(data.cohortDate, 'short')}</div>
      <div>Period: ${data.period}</div>
      <div>Retention Rate: ${formatNumber(data.retentionRate, 'percentage')}</div>
      <div>Customers: ${formatNumber(data.customerCount, 'integer')}</div>
    `;

    this.tooltip
      .style("visibility", "visible")
      .html(content)
      .style("left", `${event.pageX + 10}px`)
      .style("top", `${event.pageY - 10}px`);
  }

  private hideTooltip(): void {
    this.tooltip.style("visibility", "hidden");
  }

  public updateData(newData: CohortHeatmapData[]): void {
    this.data = newData;
    this.setupScales();
    this.render();
  }

  public updateDimensions(newDimensions: ChartDimensions): void {
    this.dimensions = newDimensions;
    this.svg
      .attr("width", newDimensions.width)
      .attr("height", newDimensions.height)
      .attr("viewBox", `0 0 ${newDimensions.width} ${newDimensions.height}`);
    this.setupScales();
    this.render();
  }

  public destroy(): void {
    this.tooltip.remove();
    this.svg.selectAll("*").remove();
  }
}

// =====================================================
// REACT COMPONENT
// =====================================================

export default function D3CohortHeatmap({
  data,
  width = 800,
  height = 400,
  title = "Cohort Retention Heatmap",
  className = "",
  onCellClick,
  onCellHover
}: D3CohortHeatmapProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<CohortHeatmapChart | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !containerRef.current || !data.length) {
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const dimensions: ChartDimensions = {
        width,
        height,
        margin: { top: 40, right: 220, bottom: 60, left: 80 }
      };

      chartRef.current = new CohortHeatmapChart(
        svgRef.current,
        containerRef.current,
        dimensions,
        data
      );

      loading.value = false;
    } catch (err) {
      console.error("Error creating cohort heatmap:", err);
      error.value = err instanceof Error ? err.message : "Unknown error";
      loading.value = false;
    }

    return () => {
      chartRef.current?.destroy();
    };
  }, [data, width, height]);

  // Handle cell click events
  useEffect(() => {
    if (!containerRef.current || !onCellClick) return;

    const handleCellClick = (event: CustomEvent) => {
      const { data: cellData, event: mouseEvent } = event.detail;
      onCellClick(cellData, mouseEvent);
    };

    containerRef.current.addEventListener("cellClick", handleCellClick as EventListener);
    
    return () => {
      containerRef.current?.removeEventListener("cellClick", handleCellClick as EventListener);
    };
  }, [onCellClick]);

  if (loading.value) {
    return (
      <div className={`cohort-heatmap-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading heatmap...</span>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div className={`cohort-heatmap-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <p className="text-red-700 font-medium">Error Loading Heatmap</p>
            <p className="text-red-600 text-sm mt-1">{error.value}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data.length) {
    return (
      <div className={`cohort-heatmap-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <p className="text-gray-600 font-medium">No Cohort Data Available</p>
            <p className="text-gray-500 text-sm mt-1">Heatmap will appear when data is loaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`cohort-heatmap-container relative ${className}`} ref={containerRef}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      <svg
        ref={svgRef}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
    </div>
  );
}
