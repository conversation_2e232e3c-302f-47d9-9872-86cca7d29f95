// D3.js Funnel Chart Visualization Component
// Week 17-18 Implementation: Interactive funnel showing conversion rates at each step

import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";
import { 
  ChartDimensions,
  DEFAULT_CHART_CONFIG,
  getInnerDimensions,
  formatNumber,
  animateEntrance,
  addHoverEffects
} from "../../utils/d3-base.ts";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface FunnelStepData {
  stepId: string;
  stepName: string;
  stepOrder: number;
  totalUsers: number;
  convertedUsers: number;
  conversionRate: number;
  dropoffRate: number;
}

export interface D3FunnelChartProps {
  data: FunnelStepData[];
  width?: number;
  height?: number;
  title?: string;
  className?: string;
  showConversionRates?: boolean;
  showDropoffRates?: boolean;
  colorScheme?: 'gradient' | 'categorical';
  onStepClick?: (step: FunnelStepData, event: MouseEvent) => void;
  onStepHover?: (step: FunnelStepData | null) => void;
}

// =====================================================
// FUNNEL CHART CLASS
// =====================================================

class FunnelChart {
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private container: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>;
  private dimensions: ChartDimensions;
  private data: FunnelStepData[];
  private colorScheme: 'gradient' | 'categorical';
  private showConversionRates: boolean;
  private showDropoffRates: boolean;

  constructor(
    svgElement: SVGSVGElement,
    containerElement: HTMLDivElement,
    dimensions: ChartDimensions,
    data: FunnelStepData[],
    options: {
      colorScheme?: 'gradient' | 'categorical';
      showConversionRates?: boolean;
      showDropoffRates?: boolean;
    } = {}
  ) {
    this.svg = d3.select(svgElement);
    this.container = d3.select(containerElement);
    this.dimensions = dimensions;
    this.data = data.sort((a, b) => a.stepOrder - b.stepOrder);
    this.colorScheme = options.colorScheme || 'gradient';
    this.showConversionRates = options.showConversionRates !== false;
    this.showDropoffRates = options.showDropoffRates !== false;
    
    // Create tooltip
    this.tooltip = this.container
      .append("div")
      .attr("class", "funnel-chart-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background-color", "rgba(0, 0, 0, 0.9)")
      .style("color", "white")
      .style("padding", "12px")
      .style("border-radius", "6px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    this.initialize();
  }

  private initialize(): void {
    const { width, height } = this.dimensions;
    
    this.svg
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style("max-width", "100%")
      .style("height", "auto");

    this.render();
  }

  public render(): void {
    this.svg.selectAll("*").remove();

    const { margin } = this.dimensions;
    const g = this.svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    this.renderFunnelSteps(g);
    this.renderConnectors(g);
    this.renderLabels(g);
    
    if (this.showConversionRates || this.showDropoffRates) {
      this.renderMetrics(g);
    }
  }

  private renderFunnelSteps(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);
    
    const maxUsers = Math.max(...this.data.map(d => d.totalUsers));
    const stepHeight = innerHeight / this.data.length;
    const maxStepWidth = innerWidth * 0.8;

    // Create color scale
    const colorScale = this.colorScheme === 'gradient' 
      ? d3.scaleSequential(d3.interpolateBlues)
          .domain([0, this.data.length - 1])
      : d3.scaleOrdinal(d3.schemeCategory10)
          .domain(this.data.map(d => d.stepId));

    const steps = g.selectAll(".funnel-step")
      .data(this.data)
      .enter()
      .append("g")
      .attr("class", "funnel-step")
      .attr("transform", (d, i) => `translate(0, ${i * stepHeight})`);

    // Calculate step widths based on user counts
    const stepWidths = this.data.map(d => (d.totalUsers / maxUsers) * maxStepWidth);

    // Create trapezoid paths for funnel steps
    steps.each(function(d, i) {
      const step = d3.select(this);
      const currentWidth = stepWidths[i];
      const nextWidth = i < stepWidths.length - 1 ? stepWidths[i + 1] : currentWidth;
      const centerX = innerWidth / 2;
      
      // Create trapezoid path
      const path = `
        M ${centerX - currentWidth / 2} 0
        L ${centerX + currentWidth / 2} 0
        L ${centerX + nextWidth / 2} ${stepHeight * 0.8}
        L ${centerX - nextWidth / 2} ${stepHeight * 0.8}
        Z
      `;

      step.append("path")
        .attr("d", path)
        .style("fill", colorScale(i))
        .style("stroke", "#fff")
        .style("stroke-width", 2)
        .style("cursor", "pointer");
    });

    // Add hover effects
    addHoverEffects(
      steps.selectAll("path"),
      (event, d) => {
        d3.select(event.currentTarget).style("opacity", 0.8);
        this.showTooltip(d, event);
      },
      (event, d) => {
        d3.select(event.currentTarget).style("opacity", 1);
        this.hideTooltip();
      }
    );

    // Add click handlers
    steps.selectAll("path").on("click", (event, d) => {
      const customEvent = new CustomEvent("stepClick", { detail: { step: d, event } });
      this.container.node()?.dispatchEvent(customEvent);
    });

    // Animate entrance
    animateEntrance(steps.selectAll("path"));
  }

  private renderConnectors(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);
    const stepHeight = innerHeight / this.data.length;

    // Add connecting lines between steps
    for (let i = 0; i < this.data.length - 1; i++) {
      const currentStep = this.data[i];
      const nextStep = this.data[i + 1];
      
      if (this.showDropoffRates && currentStep.dropoffRate > 0) {
        // Add dropoff indicator
        g.append("text")
          .attr("class", "dropoff-indicator")
          .attr("x", innerWidth - 10)
          .attr("y", (i + 0.5) * stepHeight + stepHeight * 0.4)
          .attr("text-anchor", "end")
          .style("font-size", "11px")
          .style("fill", DEFAULT_CHART_CONFIG.colors.danger)
          .style("font-weight", "500")
          .text(`-${formatNumber(currentStep.dropoffRate, 'percentage')}`);
      }
    }
  }

  private renderLabels(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);
    const stepHeight = innerHeight / this.data.length;

    // Step name labels
    g.selectAll(".step-label")
      .data(this.data)
      .enter()
      .append("text")
      .attr("class", "step-label")
      .attr("x", innerWidth / 2)
      .attr("y", (d, i) => i * stepHeight + stepHeight * 0.3)
      .attr("text-anchor", "middle")
      .style("font-size", "14px")
      .style("font-weight", "600")
      .style("fill", "#fff")
      .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.5)")
      .style("pointer-events", "none")
      .text(d => d.stepName);

    // User count labels
    g.selectAll(".user-count-label")
      .data(this.data)
      .enter()
      .append("text")
      .attr("class", "user-count-label")
      .attr("x", innerWidth / 2)
      .attr("y", (d, i) => i * stepHeight + stepHeight * 0.5)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", "#fff")
      .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.5)")
      .style("pointer-events", "none")
      .text(d => formatNumber(d.totalUsers, 'integer'));
  }

  private renderMetrics(g: d3.Selection<SVGGElement, unknown, null, undefined>): void {
    const { width: innerWidth, height: innerHeight } = getInnerDimensions(this.dimensions);
    const stepHeight = innerHeight / this.data.length;

    if (this.showConversionRates) {
      // Conversion rate labels (left side)
      g.selectAll(".conversion-rate-label")
        .data(this.data.slice(1)) // Skip first step (no conversion from previous)
        .enter()
        .append("text")
        .attr("class", "conversion-rate-label")
        .attr("x", 10)
        .attr("y", (d, i) => (i + 1) * stepHeight + stepHeight * 0.3)
        .style("font-size", "11px")
        .style("font-weight", "500")
        .style("fill", DEFAULT_CHART_CONFIG.colors.success)
        .text(d => `${formatNumber(d.conversionRate, 'percentage')} conversion`);
    }

    // Overall funnel metrics
    const totalConversion = this.data.length > 1 
      ? (this.data[this.data.length - 1].totalUsers / this.data[0].totalUsers) * 100
      : 0;

    g.append("text")
      .attr("class", "overall-conversion")
      .attr("x", innerWidth / 2)
      .attr("y", innerHeight + 20)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "600")
      .style("fill", "#374151")
      .text(`Overall Conversion: ${formatNumber(totalConversion, 'percentage')}`);
  }

  private showTooltip(data: FunnelStepData, event: MouseEvent): void {
    const content = `
      <div class="font-semibold mb-1">${data.stepName}</div>
      <div>Total Users: ${formatNumber(data.totalUsers, 'integer')}</div>
      <div>Converted: ${formatNumber(data.convertedUsers, 'integer')}</div>
      <div>Conversion Rate: ${formatNumber(data.conversionRate, 'percentage')}</div>
      <div>Dropoff Rate: ${formatNumber(data.dropoffRate, 'percentage')}</div>
    `;

    this.tooltip
      .style("visibility", "visible")
      .html(content)
      .style("left", `${event.pageX + 10}px`)
      .style("top", `${event.pageY - 10}px`);
  }

  private hideTooltip(): void {
    this.tooltip.style("visibility", "hidden");
  }

  public updateData(newData: FunnelStepData[]): void {
    this.data = newData.sort((a, b) => a.stepOrder - b.stepOrder);
    this.render();
  }

  public updateDimensions(newDimensions: ChartDimensions): void {
    this.dimensions = newDimensions;
    this.svg
      .attr("width", newDimensions.width)
      .attr("height", newDimensions.height)
      .attr("viewBox", `0 0 ${newDimensions.width} ${newDimensions.height}`);
    this.render();
  }

  public destroy(): void {
    this.tooltip.remove();
    this.svg.selectAll("*").remove();
  }
}

// =====================================================
// REACT COMPONENT
// =====================================================

export default function D3FunnelChart({
  data,
  width = 600,
  height = 400,
  title = "Conversion Funnel",
  className = "",
  showConversionRates = true,
  showDropoffRates = true,
  colorScheme = 'gradient',
  onStepClick,
  onStepHover
}: D3FunnelChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<FunnelChart | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !containerRef.current || !data.length) {
      return;
    }

    try {
      loading.value = true;
      error.value = null;

      const dimensions: ChartDimensions = {
        width,
        height,
        margin: { top: 20, right: 30, bottom: 50, left: 30 }
      };

      chartRef.current = new FunnelChart(
        svgRef.current,
        containerRef.current,
        dimensions,
        data,
        { colorScheme, showConversionRates, showDropoffRates }
      );

      loading.value = false;
    } catch (err) {
      console.error("Error creating funnel chart:", err);
      error.value = err instanceof Error ? err.message : "Unknown error";
      loading.value = false;
    }

    return () => {
      chartRef.current?.destroy();
    };
  }, [data, width, height, colorScheme, showConversionRates, showDropoffRates]);

  // Handle step click events
  useEffect(() => {
    if (!containerRef.current || !onStepClick) return;

    const handleStepClick = (event: CustomEvent) => {
      const { step, event: mouseEvent } = event.detail;
      onStepClick(step, mouseEvent);
    };

    containerRef.current.addEventListener("stepClick", handleStepClick as EventListener);
    
    return () => {
      containerRef.current?.removeEventListener("stepClick", handleStepClick as EventListener);
    };
  }, [onStepClick]);

  if (loading.value) {
    return (
      <div className={`funnel-chart-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading funnel chart...</span>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div className={`funnel-chart-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg border border-red-200">
          <div className="text-center">
            <p className="text-red-700 font-medium">Error Loading Funnel Chart</p>
            <p className="text-red-600 text-sm mt-1">{error.value}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data.length) {
    return (
      <div className={`funnel-chart-container ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>}
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <p className="text-gray-600 font-medium">No Funnel Data Available</p>
            <p className="text-gray-500 text-sm mt-1">Funnel chart will appear when data is loaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`funnel-chart-container relative ${className}`} ref={containerRef}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      <svg
        ref={svgRef}
        className="border border-gray-200 rounded-lg bg-white shadow-sm"
        style={{ maxWidth: "100%", height: "auto" }}
      />
    </div>
  );
}
