// D3.js Base Utilities for Enhanced Analytics Visualizations
// Week 17-18 Implementation: Foundation for all D3.js components

import * as d3 from "d3";

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface ChartDimensions {
  width: number;
  height: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface ChartData {
  id: string;
  value: number;
  label: string;
  timestamp?: string;
  category?: string;
  metadata?: Record<string, any>;
}

export interface TimeSeriesData {
  timestamp: string;
  value: number;
  label?: string;
  confidence?: {
    lower: number;
    upper: number;
  };
}

export interface CohortData {
  cohortId: string;
  cohortDate: string;
  period: number;
  retentionRate: number;
  customerCount: number;
  revenue?: number;
}

export interface CLVData {
  customerId: string;
  clv: number;
  segment: string;
  acquisitionDate: string;
  predictedChurn?: number;
  confidence?: number;
}

export interface FunnelStepData {
  stepId: string;
  stepName: string;
  stepOrder: number;
  totalUsers: number;
  convertedUsers: number;
  conversionRate: number;
  dropoffRate: number;
}

export interface PredictionData {
  id: string;
  type: 'churn' | 'revenue' | 'behavior';
  value: number;
  confidence: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

// =====================================================
// CHART CONFIGURATION
// =====================================================

export const DEFAULT_CHART_CONFIG = {
  dimensions: {
    width: 800,
    height: 400,
    margin: { top: 20, right: 30, bottom: 40, left: 50 }
  },
  colors: {
    primary: "#3b82f6",
    secondary: "#10b981",
    accent: "#f59e0b",
    danger: "#ef4444",
    warning: "#f97316",
    success: "#22c55e",
    neutral: "#6b7280"
  },
  animation: {
    duration: 750,
    ease: d3.easeQuadInOut
  },
  responsive: {
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1440
    }
  }
} as const;

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Calculate inner dimensions based on chart dimensions and margins
 */
export function getInnerDimensions(dimensions: ChartDimensions) {
  return {
    width: dimensions.width - dimensions.margin.left - dimensions.margin.right,
    height: dimensions.height - dimensions.margin.top - dimensions.margin.bottom
  };
}

/**
 * Create responsive chart dimensions based on container width
 */
export function createResponsiveDimensions(
  containerWidth: number,
  aspectRatio: number = 2,
  margin = DEFAULT_CHART_CONFIG.dimensions.margin
): ChartDimensions {
  const width = Math.max(300, Math.min(containerWidth, 1200));
  const height = width / aspectRatio;
  
  return {
    width,
    height,
    margin
  };
}

/**
 * Format numbers for display in charts
 */
export function formatNumber(value: number, type: 'currency' | 'percentage' | 'integer' | 'decimal' = 'decimal'): string {
  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(value);
    
    case 'percentage':
      return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 2
      }).format(value / 100);
    
    case 'integer':
      return new Intl.NumberFormat('en-US', {
        maximumFractionDigits: 0
      }).format(value);
    
    case 'decimal':
    default:
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(value);
  }
}

/**
 * Format dates for chart axes and tooltips
 */
export function formatDate(date: string | Date, format: 'short' | 'medium' | 'long' = 'medium'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    case 'long':
      return dateObj.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    case 'medium':
    default:
      return dateObj.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
  }
}

/**
 * Create color scale for categorical data
 */
export function createColorScale(categories: string[], colorScheme = DEFAULT_CHART_CONFIG.colors) {
  const colors = Object.values(colorScheme);
  return d3.scaleOrdinal<string>()
    .domain(categories)
    .range(colors);
}

/**
 * Create time scale for time series data
 */
export function createTimeScale(data: TimeSeriesData[], width: number) {
  const extent = d3.extent(data, d => new Date(d.timestamp)) as [Date, Date];
  return d3.scaleTime()
    .domain(extent)
    .range([0, width]);
}

/**
 * Create linear scale for numeric data
 */
export function createLinearScale(data: number[], height: number, nice = true) {
  const extent = d3.extent(data) as [number, number];
  const scale = d3.scaleLinear()
    .domain(extent)
    .range([height, 0]);
  
  return nice ? scale.nice() : scale;
}

/**
 * Create band scale for categorical data
 */
export function createBandScale(categories: string[], width: number, padding = 0.1) {
  return d3.scaleBand()
    .domain(categories)
    .range([0, width])
    .padding(padding);
}

// =====================================================
// ANIMATION UTILITIES
// =====================================================

/**
 * Animate chart entrance
 */
export function animateEntrance(selection: d3.Selection<any, any, any, any>, delay = 0) {
  return selection
    .style("opacity", 0)
    .transition()
    .duration(DEFAULT_CHART_CONFIG.animation.duration)
    .delay(delay)
    .ease(DEFAULT_CHART_CONFIG.animation.ease)
    .style("opacity", 1);
}

/**
 * Animate value changes
 */
export function animateValueChange(
  selection: d3.Selection<any, any, any, any>,
  property: string,
  newValue: any,
  duration = DEFAULT_CHART_CONFIG.animation.duration
) {
  return selection
    .transition()
    .duration(duration)
    .ease(DEFAULT_CHART_CONFIG.animation.ease)
    .attr(property, newValue);
}

// =====================================================
// INTERACTION UTILITIES
// =====================================================

/**
 * Add hover effects to chart elements
 */
export function addHoverEffects(
  selection: d3.Selection<any, any, any, any>,
  onMouseEnter?: (event: MouseEvent, data: any) => void,
  onMouseLeave?: (event: MouseEvent, data: any) => void
) {
  return selection
    .style("cursor", "pointer")
    .on("mouseenter", function(event, d) {
      d3.select(this)
        .transition()
        .duration(200)
        .style("opacity", 0.8);
      
      onMouseEnter?.(event, d);
    })
    .on("mouseleave", function(event, d) {
      d3.select(this)
        .transition()
        .duration(200)
        .style("opacity", 1);
      
      onMouseLeave?.(event, d);
    });
}

/**
 * Create tooltip for chart elements
 */
export function createTooltip(container: HTMLElement) {
  return d3.select(container)
    .append("div")
    .attr("class", "chart-tooltip")
    .style("position", "absolute")
    .style("visibility", "hidden")
    .style("background-color", "rgba(0, 0, 0, 0.8)")
    .style("color", "white")
    .style("padding", "8px 12px")
    .style("border-radius", "4px")
    .style("font-size", "12px")
    .style("pointer-events", "none")
    .style("z-index", "1000");
}

/**
 * Show tooltip at mouse position
 */
export function showTooltip(
  tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>,
  content: string,
  event: MouseEvent
) {
  tooltip
    .style("visibility", "visible")
    .html(content)
    .style("left", `${event.pageX + 10}px`)
    .style("top", `${event.pageY - 10}px`);
}

/**
 * Hide tooltip
 */
export function hideTooltip(tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>) {
  tooltip.style("visibility", "hidden");
}

// =====================================================
// PERFORMANCE UTILITIES
// =====================================================

/**
 * Debounce function for resize events
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for scroll/interaction events
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
